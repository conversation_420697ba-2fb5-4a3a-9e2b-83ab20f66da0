package com.tqhit.battery.one.features.emoji.data.service

import com.google.gson.Gson
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.features.emoji.domain.model.EmojiItem
import com.tqhit.battery.one.features.emoji.domain.model.EmojiCustomFields
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * Unit tests for EmojiItemService.
 * Tests Firebase Remote Config integration, fallback mechanisms, and error handling.
 * 
 * Following established testing patterns:
 * - Uses MockK for mocking dependencies
 * - Tests both success and failure scenarios
 * - Verifies fallback behavior
 * - Uses runTest for coroutine testing
 * - Tests comprehensive error handling mechanisms
 */
class EmojiItemServiceTest {

    private lateinit var remoteConfigHelper: FirebaseRemoteConfigHelper
    private lateinit var gson: Gson
    private lateinit var emojiItemService: EmojiItemService

    @Before
    fun setUp() {
        remoteConfigHelper = mockk()
        gson = Gson() // Use real Gson for JSON parsing
        emojiItemService = EmojiItemService(remoteConfigHelper, gson)
    }

    @Test
    fun `getEmojiItemsByCategory returns parsed items when remote config has valid data`() = runTest {
        // Arrange
        val validJson = """
            [
                {
                    "id": "emoji_1",
                    "category_id": "hot_category",
                    "priority": 1,
                    "name": "Fire Emoji",
                    "thumbnail": "https://example.com/thumb1.png",
                    "photo": "https://example.com/photo1.png",
                    "status": true,
                    "custom_fields": {
                        "battery": "https://example.com/battery1.png",
                        "emoji": "https://example.com/emoji1.png"
                    }
                },
                {
                    "id": "emoji_2",
                    "category_id": "hot_category",
                    "priority": 2,
                    "name": "Star Emoji",
                    "thumbnail": "https://example.com/thumb2.png",
                    "photo": "https://example.com/photo2.png",
                    "status": true,
                    "custom_fields": {
                        "battery": "https://example.com/battery2.png",
                        "emoji": "https://example.com/emoji2.png"
                    }
                }
            ]
        """.trimIndent()
        
        every { remoteConfigHelper.getString("hot_category") } returns validJson

        // Act
        val result = emojiItemService.getEmojiItemsByCategory("hot_category")

        // Assert
        assertEquals(2, result.size)
        
        val firstItem = result[0]
        assertEquals("emoji_1", firstItem.id)
        assertEquals("hot_category", firstItem.categoryId)
        assertEquals("Fire Emoji", firstItem.name)
        assertEquals(1, firstItem.priority)
        assertTrue(firstItem.status)
        assertEquals("https://example.com/thumb1.png", firstItem.thumbnail)
        assertEquals("https://example.com/photo1.png", firstItem.photo)
        assertEquals("https://example.com/battery1.png", firstItem.customFields?.battery)
        assertEquals("https://example.com/emoji1.png", firstItem.customFields?.emoji)
        
        verify { remoteConfigHelper.getString("hot_category") }
    }

    @Test
    fun `getEmojiItemsByCategory filters out disabled items`() = runTest {
        // Arrange
        val jsonWithDisabledItem = """
            [
                {
                    "id": "emoji_1",
                    "category_id": "hot_category",
                    "priority": 1,
                    "name": "Active Emoji",
                    "thumbnail": "https://example.com/thumb1.png",
                    "photo": "https://example.com/photo1.png",
                    "status": true,
                    "custom_fields": {
                        "battery": "https://example.com/battery1.png",
                        "emoji": "https://example.com/emoji1.png"
                    }
                },
                {
                    "id": "emoji_2",
                    "category_id": "hot_category",
                    "priority": 2,
                    "name": "Disabled Emoji",
                    "thumbnail": "https://example.com/thumb2.png",
                    "photo": "https://example.com/photo2.png",
                    "status": false,
                    "custom_fields": {
                        "battery": "https://example.com/battery2.png",
                        "emoji": "https://example.com/emoji2.png"
                    }
                }
            ]
        """.trimIndent()
        
        every { remoteConfigHelper.getString("hot_category") } returns jsonWithDisabledItem

        // Act
        val result = emojiItemService.getEmojiItemsByCategory("hot_category")

        // Assert
        assertEquals(1, result.size)
        assertEquals("emoji_1", result[0].id)
        assertEquals("Active Emoji", result[0].name)
        assertTrue(result[0].status)
    }

    @Test
    fun `getEmojiItemsByCategory sorts items by priority`() = runTest {
        // Arrange
        val jsonWithUnsortedItems = """
            [
                {
                    "id": "emoji_3",
                    "category_id": "hot_category",
                    "priority": 3,
                    "name": "Third Priority",
                    "thumbnail": "https://example.com/thumb3.png",
                    "photo": "https://example.com/photo3.png",
                    "status": true,
                    "custom_fields": {
                        "battery": "https://example.com/battery3.png",
                        "emoji": "https://example.com/emoji3.png"
                    }
                },
                {
                    "id": "emoji_1",
                    "category_id": "hot_category",
                    "priority": 1,
                    "name": "First Priority",
                    "thumbnail": "https://example.com/thumb1.png",
                    "photo": "https://example.com/photo1.png",
                    "status": true,
                    "custom_fields": {
                        "battery": "https://example.com/battery1.png",
                        "emoji": "https://example.com/emoji1.png"
                    }
                },
                {
                    "id": "emoji_2",
                    "category_id": "hot_category",
                    "priority": 2,
                    "name": "Second Priority",
                    "thumbnail": "https://example.com/thumb2.png",
                    "photo": "https://example.com/photo2.png",
                    "status": true,
                    "custom_fields": {
                        "battery": "https://example.com/battery2.png",
                        "emoji": "https://example.com/emoji2.png"
                    }
                }
            ]
        """.trimIndent()
        
        every { remoteConfigHelper.getString("hot_category") } returns jsonWithUnsortedItems

        // Act
        val result = emojiItemService.getEmojiItemsByCategory("hot_category")

        // Assert
        assertEquals(3, result.size)
        assertEquals("emoji_1", result[0].id) // priority 1
        assertEquals("emoji_2", result[1].id) // priority 2
        assertEquals("emoji_3", result[2].id) // priority 3
    }

    @Test
    fun `getEmojiItemsByCategory returns empty list when remote config is empty`() = runTest {
        // Arrange
        every { remoteConfigHelper.getString("hot_category") } returns ""

        // Act
        val result = emojiItemService.getEmojiItemsByCategory("hot_category")

        // Assert
        assertTrue(result.isEmpty())
    }

    @Test
    fun `getEmojiItemsByCategory returns empty list when remote config throws exception`() = runTest {
        // Arrange
        every { remoteConfigHelper.getString("hot_category") } throws RuntimeException("Network error")

        // Act
        val result = emojiItemService.getEmojiItemsByCategory("hot_category")

        // Assert
        assertTrue(result.isEmpty())
    }

    @Test
    fun `getEmojiItemsByCategory returns empty list when JSON is invalid`() = runTest {
        // Arrange
        val invalidJson = "{ invalid json }"
        every { remoteConfigHelper.getString("hot_category") } returns invalidJson

        // Act
        val result = emojiItemService.getEmojiItemsByCategory("hot_category")

        // Assert
        assertTrue(result.isEmpty())
    }

    // Note: isEmojiItemDataAvailable method doesn't exist in EmojiItemService
    // These tests are removed as the method is not implemented

    @Test
    fun `getAvailableCategoryIds returns expected category list`() {
        // Act
        val result = emojiItemService.getAvailableCategoryIds()

        // Assert
        assertTrue(result.contains("hot_category"))
        assertTrue(result.contains("character_category"))
        assertTrue(result.contains("animal_category"))
        assertTrue(result.contains("heart_category"))
        assertTrue(result.contains("cute_category"))
        assertTrue(result.size >= 5) // Should have at least 5 categories
    }

    @Test
    fun `emoji item validation works correctly`() {
        // Test valid item
        val validItem = EmojiItem(
            id = "test_id",
            categoryId = "test_category",
            priority = 1,
            name = "Test Name",
            thumbnail = "https://example.com/thumb.png",
            photo = "https://example.com/photo.png",
            status = true,
            customFields = mapOf(
                "battery" to "https://example.com/battery.png",
                "emoji" to "https://example.com/emoji.png"
            )
        )
        assertTrue(validItem.isValid())
        
        // Test invalid item (empty required fields)
        val invalidItem = EmojiItem(
            id = "",
            categoryId = "",
            priority = -1,
            name = "",
            thumbnail = "",
            photo = "",
            status = true,
            customFields = emptyMap()
        )
        assertFalse(invalidItem.isValid())
    }

    @Test
    fun `emoji item toBatteryStyle conversion works correctly`() {
        // Test conversion
        val emojiItem = EmojiItem(
            id = "test_emoji",
            categoryId = "hot_category",
            priority = 1,
            name = "Test Emoji",
            thumbnail = "https://example.com/thumb.png",
            photo = "https://example.com/photo.png",
            status = true,
            customFields = mapOf(
                "battery" to "https://example.com/battery.png",
                "emoji" to "https://example.com/emoji.png"
            )
        )
        
        val batteryStyle = emojiItem.toBatteryStyle()
        
        assertEquals("test_emoji", batteryStyle.id)
        assertEquals("Test Emoji", batteryStyle.name)
        assertEquals("https://example.com/thumb.png", batteryStyle.galleryThumbnailUrl)
        assertEquals("https://example.com/photo.png", batteryStyle.customizePreviewUrl)
        assertEquals("https://example.com/battery.png", batteryStyle.batteryImageUrl)
        assertEquals("https://example.com/emoji.png", batteryStyle.emojiImageUrl)
    }
}
