package com.tqhit.battery.one.features.emoji.presentation.gallery

import android.content.Context
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.features.emoji.data.service.EmojiCategoryService
import com.tqhit.battery.one.features.emoji.data.service.EmojiItemService
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory
import com.tqhit.battery.one.features.emoji.domain.model.EmojiCategory
import com.tqhit.battery.one.features.emoji.domain.model.EmojiItem
import com.tqhit.battery.one.features.emoji.domain.model.EmojiCustomFields
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Rule
import org.junit.Test

/**
 * Unit tests for BatteryGalleryViewModel.
 * Tests MVI pattern implementation, navigation event handling, error handling mechanisms,
 * and comprehensive state management.
 * 
 * Following established testing patterns:
 * - Uses MockK for mocking dependencies
 * - Tests coroutine-based operations with TestDispatcher
 * - Verifies MVI state transitions
 * - Tests navigation event consumption
 * - Validates comprehensive error handling
 */
@OptIn(ExperimentalCoroutinesApi::class)
class BatteryGalleryViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = StandardTestDispatcher()

    private lateinit var mockContext: Context
    private lateinit var mockAppRepository: AppRepository
    private lateinit var mockEmojiCategoryService: EmojiCategoryService
    private lateinit var mockEmojiItemService: EmojiItemService
    private lateinit var viewModel: BatteryGalleryViewModel

    // Test data
    private val testCategories = listOf(
        EmojiCategory("hot_category", 1, "🔥 HOT", true, false),
        EmojiCategory("character_category", 2, "Character", true, false),
        EmojiCategory("animal_category", 3, "Animal", true, true)
    )

    private val testEmojiItems = listOf(
        EmojiItem(
            id = "emoji_1",
            categoryId = "hot_category",
            priority = 1,
            name = "Test Emoji 1",
            thumbnail = "https://example.com/thumb1.png",
            photo = "https://example.com/photo1.png",
            status = true,
            customFields = EmojiCustomFields(
                battery = "https://example.com/battery1.png",
                emoji = "https://example.com/emoji1.png"
            )
        )
    )

    private val testBatteryStyle = BatteryStyle(
        id = "style_1",
        name = "Test Style",
        category = BatteryStyleCategory.HOT,
        galleryThumbnailUrl = "https://example.com/thumb.png",
        customizePreviewUrl = "https://example.com/preview.png",
        batteryImageUrl = "https://example.com/battery.png",
        emojiImageUrl = "https://example.com/emoji.png",
        isPremium = false
    )

    @Before
    fun setUp() {
        Dispatchers.setMain(testDispatcher)
        
        mockContext = mockk(relaxed = true)
        mockAppRepository = mockk(relaxed = true)
        mockEmojiCategoryService = mockk(relaxed = true)
        mockEmojiItemService = mockk(relaxed = true)

        // Setup default mock responses
        coEvery { mockEmojiCategoryService.getEmojiCategories() } returns testCategories
        coEvery { mockEmojiItemService.getEmojiItemsByCategory(any()) } returns testEmojiItems
        every { mockContext.applicationContext } returns mockContext
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    private fun createViewModel(): BatteryGalleryViewModel {
        return BatteryGalleryViewModel(
            context = mockContext,
            appRepository = mockAppRepository,
            emojiCategoryService = mockEmojiCategoryService,
            emojiItemService = mockEmojiItemService
        )
    }

    @Test
    fun `initial state should be correct`() = runTest {
        // When
        viewModel = createViewModel()
        
        // Then
        val initialState = viewModel.uiState.first()
        assertFalse(initialState.isLoading)
        assertTrue(initialState.displayedStyles.isEmpty())
        assertNull(initialState.errorMessage)
        assertNull(initialState.navigationEvent)
        assertEquals(BatteryStyleCategory.HOT, initialState.selectedCategory)
    }

    @Test
    fun `loadEmojiCategories should update categories successfully`() = runTest {
        // When
        viewModel = createViewModel()
        advanceUntilIdle()
        
        // Then
        val categories = viewModel.emojiCategories.first()
        assertEquals(testCategories.size, categories.size)
        assertEquals("hot_category", categories[0].id)
        verify { mockEmojiCategoryService.getEmojiCategories() }
    }

    @Test
    fun `selectBatteryStyle with free style should trigger customize navigation`() = runTest {
        // Given
        viewModel = createViewModel()
        val freeStyle = testBatteryStyle.copy(isPremium = false)
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.SelectBatteryStyle(freeStyle))
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.first()
        assertTrue(state.navigationEvent is NavigationEvent.NavigateToCustomize)
        assertEquals(freeStyle, (state.navigationEvent as NavigationEvent.NavigateToCustomize).style)
    }

    @Test
    fun `selectBatteryStyle with premium style should trigger premium dialog`() = runTest {
        // Given
        viewModel = createViewModel()
        val premiumStyle = testBatteryStyle.copy(isPremium = true)
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.SelectBatteryStyle(premiumStyle))
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.first()
        assertTrue(state.navigationEvent is NavigationEvent.ShowPremiumDialog)
        assertEquals(premiumStyle, (state.navigationEvent as NavigationEvent.ShowPremiumDialog).style)
    }

    @Test
    fun `consumeNavigationEvent should clear navigation event`() = runTest {
        // Given
        viewModel = createViewModel()
        viewModel.handleEvent(BatteryGalleryEvent.SelectBatteryStyle(testBatteryStyle))
        advanceUntilIdle()
        
        // Verify navigation event is set
        val stateWithEvent = viewModel.uiState.first()
        assertNotNull(stateWithEvent.navigationEvent)
        
        // When
        viewModel.consumeNavigationEvent()
        advanceUntilIdle()
        
        // Then
        val stateAfterConsume = viewModel.uiState.first()
        assertNull(stateAfterConsume.navigationEvent)
    }

    @Test
    fun `error handling should set appropriate error state for network error`() = runTest {
        // Given
        val networkException = RuntimeException("Network error")
        coEvery { mockEmojiCategoryService.getEmojiCategories() } throws networkException
        
        // When
        viewModel = createViewModel()
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.first()
        assertTrue(state.hasError)
        assertEquals(ErrorType.NETWORK_ERROR, state.errorType)
        assertTrue(state.isErrorRecoverable)
        assertFalse(state.isLoading)
    }

    @Test
    fun `error handling should set appropriate error state for parsing error`() = runTest {
        // Given
        val parsingException = com.google.gson.JsonSyntaxException("Invalid JSON")
        coEvery { mockEmojiItemService.getEmojiItemsByCategory(any()) } throws parsingException
        
        // When
        viewModel = createViewModel()
        viewModel.handleEvent(BatteryGalleryEvent.SelectCategory(BatteryStyleCategory.CHARACTER))
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.first()
        assertTrue(state.hasError)
        assertEquals(ErrorType.DATA_PARSING_ERROR, state.errorType)
        assertTrue(state.isErrorRecoverable)
    }

    @Test
    fun `clearError should reset error state`() = runTest {
        // Given
        val exception = RuntimeException("Test error")
        coEvery { mockEmojiCategoryService.getEmojiCategories() } throws exception
        viewModel = createViewModel()
        advanceUntilIdle()
        
        // Verify error state is set
        val errorState = viewModel.uiState.first()
        assertTrue(errorState.hasError)
        
        // When
        viewModel.clearError()
        advanceUntilIdle()
        
        // Then
        val clearedState = viewModel.uiState.first()
        assertFalse(clearedState.hasError)
        assertNull(clearedState.errorMessage)
        assertNull(clearedState.errorType)
    }

    @Test
    fun `retryLastOperation should reload data after network error`() = runTest {
        // Given
        val networkException = RuntimeException("Network error")
        coEvery { mockEmojiCategoryService.getEmojiCategories() } throws networkException andThen testCategories
        
        viewModel = createViewModel()
        advanceUntilIdle()
        
        // Verify error state
        val errorState = viewModel.uiState.first()
        assertTrue(errorState.hasError)
        
        // When
        viewModel.retryLastOperation()
        advanceUntilIdle()
        
        // Then
        val retriedState = viewModel.uiState.first()
        assertFalse(retriedState.hasError)
        val categories = viewModel.emojiCategories.first()
        assertEquals(testCategories.size, categories.size)
    }

    @Test
    fun `handleNavigationEvent should set correct navigation event`() = runTest {
        // Given
        viewModel = createViewModel()
        val navigationEvent = BatteryGalleryEvent.NavigationEvent.ShowPermissionDialog
        
        // When
        viewModel.handleEvent(navigationEvent)
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.first()
        assertTrue(state.navigationEvent is NavigationEvent.ShowPermissionDialog)
    }

    @Test
    fun `handleErrorEvent should update error message`() = runTest {
        // Given
        viewModel = createViewModel()
        val errorMessage = "Test error message"
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.ErrorEvent.ShowError(errorMessage))
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.first()
        assertEquals(errorMessage, state.errorMessage)
    }

    @Test
    fun `handleErrorEvent dismiss should clear error message`() = runTest {
        // Given
        viewModel = createViewModel()
        viewModel.handleEvent(BatteryGalleryEvent.ErrorEvent.ShowError("Test error"))
        advanceUntilIdle()
        
        // When
        viewModel.handleEvent(BatteryGalleryEvent.ErrorEvent.DismissError)
        advanceUntilIdle()
        
        // Then
        val state = viewModel.uiState.first()
        assertNull(state.errorMessage)
    }
}
