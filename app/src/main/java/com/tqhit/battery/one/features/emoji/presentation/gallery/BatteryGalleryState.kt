package com.tqhit.battery.one.features.emoji.presentation.gallery

import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyleCategory

/**
 * Types of errors that can occur in the gallery
 */
enum class ErrorType {
    NETWORK_ERROR,          // No internet connection or network timeout
    REMOTE_CONFIG_ERROR,    // Firebase Remote Config fetch failed
    DATA_PARSING_ERROR,     // Error parsing JSON data from remote config
    PERMISSION_ERROR,       // Missing required permissions
    UNKNOWN_ERROR          // Unexpected error
}

/**
 * Navigation events that can be triggered from the gallery.
 * These are one-time events that should be consumed by the UI.
 */
sealed class NavigationEvent {
    data class NavigateToCustomize(val style: BatteryStyle) : NavigationEvent()
    data class ShowPremiumDialog(val style: BatteryStyle) : NavigationEvent()
    object ShowPermissionDialog : NavigationEvent()
    object ShowInfoDialog : NavigationEvent()
    data class ShowToast(val message: String) : NavigationEvent()
}

/**
 * UI state for the Battery Gallery screen.
 * Represents all possible states of the gallery UI following MVI pattern.
 * 
 * This state class follows the established MVI patterns in the app:
 * - Immutable data class representing complete UI state
 * - Contains loading states, data, and error information
 * - Supports category filtering and search functionality
 * - Handles premium content and global toggle states
 */
data class BatteryGalleryState(
    /**
     * Whether the gallery is currently loading data from remote config or local fallback
     */
    val isLoading: Boolean = false,
    
    /**
     * Whether the initial data load has completed (used to show shimmer only on first load)
     */
    val isInitialLoadComplete: Boolean = false,
    
    /**
     * Error message to display to the user, null if no error
     */
    val errorMessage: String? = null,

    /**
     * Type of error that occurred, null if no error
     */
    val errorType: ErrorType? = null,

    /**
     * Whether the error is recoverable (user can retry)
     */
    val isErrorRecoverable: Boolean = true,
    
    /**
     * Complete list of all available battery styles
     */
    val allStyles: List<BatteryStyle> = emptyList(),
    
    /**
     * Currently displayed battery styles (filtered by category/search)
     */
    val displayedStyles: List<BatteryStyle> = emptyList(),
    
    /**
     * Available categories for filtering
     */
    val categories: List<BatteryStyleCategory> = BatteryStyleCategory.getMainFilterCategories(),
    
    /**
     * Currently selected category for filtering
     */
    val selectedCategory: BatteryStyleCategory = BatteryStyleCategory.HOT,
    
    /**
     * Current search query, empty string if no search active
     */
    val searchQuery: String = "",
    
    /**
     * Whether search mode is active (search input is visible)
     */
    val isSearchActive: Boolean = false,
    
    /**
     * Whether the global emoji battery feature is enabled
     */
    val isGlobalToggleEnabled: Boolean = false,
    
    /**
     * Whether to show only premium styles
     */
    val showOnlyPremium: Boolean = false,
    
    /**
     * Whether to show only free styles
     */
    val showOnlyFree: Boolean = false,
    
    /**
     * Whether ads should be shown (based on user's ad-free purchase status)
     */
    val shouldShowAds: Boolean = true,
    
    /**
     * Whether the refresh operation is in progress (pull-to-refresh)
     */
    val isRefreshing: Boolean = false,
    
    /**
     * Last refresh timestamp for display purposes
     */
    val lastRefreshTimestamp: Long = 0L,

    /**
     * Navigation event to be consumed by the UI.
     * Should be set to null after consumption to prevent re-triggering.
     */
    val navigationEvent: NavigationEvent? = null
) {
    
    /**
     * Checks if there are any styles to display
     */
    val hasStyles: Boolean
        get() = displayedStyles.isNotEmpty()
    
    /**
     * Checks if the gallery is in an empty state (no styles and not loading)
     */
    val isEmpty: Boolean
        get() = displayedStyles.isEmpty() && !isLoading
    
    /**
     * Checks if there's an error state
     */
    val hasError: Boolean
        get() = errorMessage != null
    
    /**
     * Gets the count of displayed styles for UI display
     */
    val displayedStylesCount: Int
        get() = displayedStyles.size
    
    /**
     * Gets the count of premium styles in the current display
     */
    val premiumStylesCount: Int
        get() = displayedStyles.count { it.isPremium }
    
    /**
     * Gets the count of free styles in the current display
     */
    val freeStylesCount: Int
        get() = displayedStyles.count { !it.isPremium }
    
    /**
     * Checks if the current category is HOT (popular styles)
     */
    val isHotCategorySelected: Boolean
        get() = selectedCategory == BatteryStyleCategory.HOT
    
    /**
     * Gets the display text for the current category
     */
    val selectedCategoryDisplayText: String
        get() = selectedCategory.getDisplayText()
    
    /**
     * Checks if any filters are active (search, premium only, free only)
     */
    val hasActiveFilters: Boolean
        get() = searchQuery.isNotBlank() || showOnlyPremium || showOnlyFree

    /**
     * Checks if there's a pending navigation event
     */
    val hasNavigationEvent: Boolean
        get() = navigationEvent != null

    /**
     * Creates a copy of this state with the navigation event consumed (set to null)
     */
    fun consumeNavigationEvent(): BatteryGalleryState {
        return copy(navigationEvent = null)
    }
    
    companion object {
        /**
         * Creates the initial state for the gallery
         */
        fun initial(): BatteryGalleryState {
            return BatteryGalleryState()
        }
        
        /**
         * Creates a loading state
         */
        fun loading(): BatteryGalleryState {
            return BatteryGalleryState(isLoading = true)
        }
        
        /**
         * Creates an error state with the given message and type
         */
        fun error(
            message: String,
            errorType: ErrorType = ErrorType.UNKNOWN_ERROR,
            isRecoverable: Boolean = true
        ): BatteryGalleryState {
            return BatteryGalleryState(
                isLoading = false,
                errorMessage = message,
                errorType = errorType,
                isErrorRecoverable = isRecoverable,
                isInitialLoadComplete = true
            )
        }

        /**
         * Creates a network error state with user-friendly message
         */
        fun networkError(): BatteryGalleryState {
            return error(
                message = "No internet connection. Please check your network and try again.",
                errorType = ErrorType.NETWORK_ERROR,
                isRecoverable = true
            )
        }

        /**
         * Creates a remote config error state with user-friendly message
         */
        fun remoteConfigError(): BatteryGalleryState {
            return error(
                message = "Unable to load latest content. Using offline data.",
                errorType = ErrorType.REMOTE_CONFIG_ERROR,
                isRecoverable = true
            )
        }

        /**
         * Creates a data parsing error state with user-friendly message
         */
        fun dataParsingError(): BatteryGalleryState {
            return error(
                message = "Content format error. Please try again later.",
                errorType = ErrorType.DATA_PARSING_ERROR,
                isRecoverable = true
            )
        }
        
        /**
         * Creates a success state with the given styles
         */
        fun success(
            allStyles: List<BatteryStyle>,
            selectedCategory: BatteryStyleCategory = BatteryStyleCategory.HOT
        ): BatteryGalleryState {
            val filteredStyles = when (selectedCategory) {
                BatteryStyleCategory.HOT -> allStyles.filter { it.isPopular }
                else -> allStyles.filter { it.category == selectedCategory }
            }
            
            return BatteryGalleryState(
                isLoading = false,
                isInitialLoadComplete = true,
                allStyles = allStyles,
                displayedStyles = filteredStyles,
                selectedCategory = selectedCategory,
                lastRefreshTimestamp = System.currentTimeMillis()
            )
        }
    }
}
